package com.xiaozhi;

import com.xiaozhi.dialogue.tts.providers.VolcengineTtsService;
import com.xiaozhi.entity.SysConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 火山引擎TTS服务测试类
 * 测试东北话和四川话音色的语音合成功能
 */
public class VolcengineTtsServiceTest {
    
    private static final Logger logger = LoggerFactory.getLogger(VolcengineTtsServiceTest.class);
    
    private SysConfig config;
    private String outputPath;
    
    @BeforeEach
    void setUp() {
        // 使用用户提供的凭据配置
        config = new SysConfig();
        config.setAppId("**********");
        config.setApiKey("88Q2q6uuQy4E0A3c_KjsZEROES3ybvmR");
        config.setApiSecret("11XIEuDgxA3BZNl9F7B3zK9DMGaDkr4O");
        
        // 设置输出路径
        outputPath = "test-audio/";
        
        // 确保输出目录存在
        File dir = new File(outputPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        
        logger.info("测试环境初始化完成 - AppId: {}", config.getAppId());
    }
    
    @Test
    void testNortheastDialectTts() throws Exception {
        logger.info("开始测试东北老铁音色");

        VolcengineTtsService ttsService = new VolcengineTtsService(config, "BV019_streaming", outputPath);

        String testText = "哎呀，这个小智真是太厉害了，老铁们给点个赞呗！";
        String audioPath = ttsService.textToSpeech(testText);

        assertNotNull(audioPath, "音频文件路径不应为空");
        assertTrue(Files.exists(Paths.get(audioPath)), "音频文件应该存在");

        File audioFile = new File(audioPath);
        assertTrue(audioFile.length() > 0, "音频文件大小应该大于0");

        logger.info("东北老铁音色测试成功 - 文件路径: {}, 文件大小: {} bytes",
                   audioPath, audioFile.length());
    }
    
    @Test
    void testSichuanDialectTts() throws Exception {
        logger.info("开始测试四川甜妹儿音色");

        VolcengineTtsService ttsService = new VolcengineTtsService(config, "BV221_streaming", outputPath);

        String testText = "哎呀，这个小智巴适得很，大家快来试试嘛！";
        String audioPath = ttsService.textToSpeech(testText);

        assertNotNull(audioPath, "音频文件路径不应为空");
        assertTrue(Files.exists(Paths.get(audioPath)), "音频文件应该存在");

        File audioFile = new File(audioPath);
        assertTrue(audioFile.length() > 0, "音频文件大小应该大于0");

        logger.info("四川甜妹儿音色测试成功 - 文件路径: {}, 文件大小: {} bytes",
                   audioPath, audioFile.length());
    }
    
    @Test
    void testStandardVoices() throws Exception {
        logger.info("开始测试标准音色");

        // 测试通用女声
        VolcengineTtsService femaleService = new VolcengineTtsService(config, "BV001_streaming", outputPath);
        String femaleAudioPath = femaleService.textToSpeech("大家好，我是小智的通用女声。");

        assertNotNull(femaleAudioPath, "女声音频文件路径不应为空");
        assertTrue(Files.exists(Paths.get(femaleAudioPath)), "女声音频文件应该存在");

        // 测试通用男声
        VolcengineTtsService maleService = new VolcengineTtsService(config, "BV002_streaming", outputPath);
        String maleAudioPath = maleService.textToSpeech("大家好，我是小智的通用男声。");

        assertNotNull(maleAudioPath, "男声音频文件路径不应为空");
        assertTrue(Files.exists(Paths.get(maleAudioPath)), "男声音频文件应该存在");

        logger.info("标准音色测试成功 - 女声: {}, 男声: {}", femaleAudioPath, maleAudioPath);
    }
    
    @Test
    void testEmptyText() {
        logger.info("开始测试空文本处理");
        
        VolcengineTtsService ttsService = new VolcengineTtsService(config, "东北话", outputPath);
        
        // 测试空文本和null文本的处理
        try {
            String result1 = ttsService.textToSpeech("");
            assertNull(result1, "空文本应该返回null");
        } catch (Exception e) {
            // 空文本可能抛出异常，这也是可以接受的
            logger.info("空文本抛出异常: {}", e.getMessage());
        }
        
        try {
            String result2 = ttsService.textToSpeech(null);
            assertNull(result2, "null文本应该返回null");
        } catch (Exception e) {
            // null文本可能抛出异常，这也是可以接受的
            logger.info("null文本抛出异常: {}", e.getMessage());
        }
        
        logger.info("空文本处理测试完成");
    }
}
