# 通用配置（所有环境共享）
server.port=8091

# Mybatis配置
mybatis.mapper-locations=classpath*:com/xiaozhi/mapper/*.xml

# 数据库连接池通用配置
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.idle-timeout=120000
spring.datasource.hikari.pool-name=DatebookHikariCP
spring.datasource.hikari.max-lifetime=120000
spring.datasource.hikari.connection-timeout=10000000
spring.datasource.hikari.connection-test-query=SELECT 1

# 日志通用配置
logging.level.root=INFO
logging.level.org.springframework=INFO
logging.level.io.github.imfangs.dify.client.impl.StreamEventDispatcher=ERROR

# 禁用Groovy模板位置检查
spring.groovy.template.check-template-location=false

# 文件上传配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=2048MB
spring.servlet.multipart.max-request-size=2048MB

spring.jmx.enabled=false

# 允许的WebSocket源（跨域配置），多个源用逗号分隔
spring.websocket.allowed-origins=*

# 添加 Spring MVC Session 配置
spring.session.cookie.http-only=true
spring.session.cookie.secure=false
spring.session.cookie.same-site=lax
spring.session.cookie.path=/

# 启用虚拟线程
spring.threads.virtual.enabled=true

# 激活的配置文件（默认为开发环境）
spring.profiles.active=dev