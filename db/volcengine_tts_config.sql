-- 火山引擎豆包TTS配置示例
-- 使用用户提供的凭据创建TTS配置

-- 插入火山引擎TTS配置
INSERT INTO `xiaozhi`.`sys_config` (
    `userId`, 
    `configType`, 
    `modelType`, 
    `provider`, 
    `configName`, 
    `configDesc`, 
    `appId`, 
    `apiKey`, 
    `apiSecret`, 
    `ak`, 
    `sk`, 
    `apiUrl`, 
    `isDefault`, 
    `state`
) VALUES (
    1, -- 假设用户ID为1，请根据实际情况修改
    'tts',
    NULL,
    'volcengine',
    '火山引擎豆包TTS',
    '支持东北话和四川话方言音色的语音合成服务',
    '**********', -- 用户提供的appId
    '88Q2q6uuQy4E0A3c_KjsZEROES3ybvmR', -- 用户提供的access_token
    '11XIEuDgxA3BZNl9F7B3zK9DMGaDkr4O', -- 用户提供的secret_key
    NULL,
    NULL,
    'https://openspeech.bytedance.com/api/v1/tts',
    '0', -- 不设为默认配置
    '1'  -- 启用状态
);

-- 创建使用东北话音色的角色示例
INSERT INTO `xiaozhi`.`sys_role` (
    `avatar`,
    `roleName`,
    `roleDesc`,
    `voiceName`,
    `userId`,
    `state`,
    `isDefault`,
    `ttsId`,
    `modelId`,
    `sttId`,
    `temperature`,
    `topP`,
    `vadEnergyTh`,
    `vadSpeechTh`,
    `vadSilenceTh`,
    `vadSilenceMs`
) VALUES (
    '/static/avatar/northeast.png', -- 头像路径，需要准备相应的头像文件
    '东北老铁',
    '使用东北话音色的智能助手，说话风格幽默风趣，充满东北特色',
    'BV019_streaming', -- 使用东北老铁音色
    1, -- 用户ID，请根据实际情况修改
    '1', -- 启用状态
    '0', -- 不设为默认角色
    (SELECT configId FROM `xiaozhi`.`sys_config` WHERE provider = 'volcengine' AND configName = '火山引擎豆包TTS' LIMIT 1), -- 关联TTS配置
    1, -- 模型ID，请根据实际情况修改
    1, -- STT配置ID，请根据实际情况修改
    0.7,
    0.9,
    0.5,
    0.5,
    0.3,
    1000
);

-- 创建使用四川话音色的角色示例
INSERT INTO `xiaozhi`.`sys_role` (
    `avatar`,
    `roleName`,
    `roleDesc`,
    `voiceName`,
    `userId`,
    `state`,
    `isDefault`,
    `ttsId`,
    `modelId`,
    `sttId`,
    `temperature`,
    `topP`,
    `vadEnergyTh`,
    `vadSpeechTh`,
    `vadSilenceTh`,
    `vadSilenceMs`
) VALUES (
    '/static/avatar/sichuan.png', -- 头像路径，需要准备相应的头像文件
    '四川甜妹儿',
    '使用四川话音色的智能助手，说话温柔甜美，充满四川特色',
    'BV221_streaming', -- 使用四川甜妹儿音色
    1, -- 用户ID，请根据实际情况修改
    '1', -- 启用状态
    '0', -- 不设为默认角色
    (SELECT configId FROM `xiaozhi`.`sys_config` WHERE provider = 'volcengine' AND configName = '火山引擎豆包TTS' LIMIT 1), -- 关联TTS配置
    1, -- 模型ID，请根据实际情况修改
    1, -- STT配置ID，请根据实际情况修改
    0.8,
    0.9,
    0.5,
    0.5,
    0.3,
    1000
);

-- 查询创建的配置和角色
SELECT 
    c.configId,
    c.configName,
    c.provider,
    c.appId,
    c.state,
    c.createTime
FROM `xiaozhi`.`sys_config` c 
WHERE c.provider = 'volcengine' 
ORDER BY c.createTime DESC;

SELECT 
    r.roleId,
    r.roleName,
    r.roleDesc,
    r.voiceName,
    r.state,
    c.configName as ttsConfigName
FROM `xiaozhi`.`sys_role` r
LEFT JOIN `xiaozhi`.`sys_config` c ON r.ttsId = c.configId
WHERE r.voiceName IN ('BV019_streaming', 'BV221_streaming')
ORDER BY r.createTime DESC;

-- 注意事项：
-- 1. 请根据实际的用户ID修改userId字段
-- 2. 请根据实际的模型ID和STT配置ID修改相应字段
-- 3. 头像文件需要单独准备并放置在指定路径
-- 4. 如果需要设置为默认配置，请将isDefault字段改为'1'
