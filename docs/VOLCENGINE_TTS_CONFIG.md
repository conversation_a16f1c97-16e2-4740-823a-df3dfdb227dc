# 火山引擎豆包TTS配置指南

## 概述

本文档介绍如何在小智项目中配置和使用火山引擎豆包TTS服务，特别是东北话和四川话方言音色。

## 配置步骤

### 1. 获取火山引擎凭据

首先需要在火山引擎控制台获取以下凭据：
- `appId`: 应用ID
- `access_token`: 访问令牌
- `secret_key`: 密钥

### 2. 在系统中添加TTS配置

1. 登录小智管理后台
2. 进入 "配置管理" -> "TTS配置"
3. 点击 "创建TTS" 标签页
4. 填写配置信息：
   - **配置名称**: 火山引擎豆包TTS
   - **配置描述**: 支持东北话和四川话的语音合成服务
   - **服务提供商**: 选择 `volcengine`
   - **APP ID**: 填入你的appId (例如: 5278532560)
   - **API密钥**: 填入你的access_token
   - **API密钥**: 填入你的secret_key

### 3. 配置角色音色

1. 进入 "角色管理"
2. 创建或编辑角色
3. 在TTS配置中选择刚创建的火山引擎配置
4. 在音色选择中可以选择以下方言音色：
   - **东北话**: 使用东北方言的男声
   - **东北老铁**: 东北方言男声的别名
   - **四川话**: 使用四川方言的女声
   - **四川甜妹儿**: 四川方言女声的别名

## 支持的音色

### 方言音色
- `东北话` / `东北老铁`: BV406_streaming (男声)
- `四川话` / `四川甜妹儿`: BV407_streaming (女声)

### 标准音色
- `通用女声`: BV001_streaming
- `通用男声`: BV002_streaming

## 测试验证

项目中包含了完整的测试用例，可以通过以下命令运行测试：

```bash
mvn test -Dtest=VolcengineTtsServiceTest
```

测试将验证：
1. 东北话音色语音合成
2. 四川话音色语音合成
3. 标准音色语音合成
4. 音色映射功能
5. 错误处理

## 音色映射机制

系统实现了智能音色映射机制：

```java
// 方言音色映射
private static final Map<String, String> DIALECT_VOICE_MAP = new HashMap<>();
static {
    // 东北话音色
    DIALECT_VOICE_MAP.put("东北话", "BV406_streaming");
    DIALECT_VOICE_MAP.put("东北老铁", "BV406_streaming");
    DIALECT_VOICE_MAP.put("northeast", "BV406_streaming");
    
    // 四川话音色
    DIALECT_VOICE_MAP.put("四川话", "BV407_streaming");
    DIALECT_VOICE_MAP.put("四川甜妹儿", "BV407_streaming");
    DIALECT_VOICE_MAP.put("sichuan", "BV407_streaming");
    
    // 通用音色
    DIALECT_VOICE_MAP.put("通用女声", "BV001_streaming");
    DIALECT_VOICE_MAP.put("通用男声", "BV002_streaming");
}
```

## 使用示例

### 代码示例

```java
// 创建配置
SysConfig config = new SysConfig();
config.setAppId("5278532560");
config.setApiKey("88Q2q6uuQy4E0A3c_KjsZEROES3ybvmR");
config.setApiSecret("11XIEuDgxA3BZNl9F7B3zK9DMGaDkr4O");

// 创建TTS服务实例
VolcengineTtsService ttsService = new VolcengineTtsService(config, "东北话", "audio/");

// 生成语音
String audioPath = ttsService.textToSpeech("哎呀，这个小智真是太厉害了，老铁们给点个赞呗！");
```

### 测试文本示例

**东北话测试文本**:
- "哎呀，这个小智真是太厉害了，老铁们给点个赞呗！"
- "老铁，这个智能音箱真是杠杠的，必须给个好评！"

**四川话测试文本**:
- "哎呀，这个小智巴适得很，大家快来试试嘛！"
- "小哥哥，这个小智好乖哦，我们一起来聊天嘛！"

## 注意事项

1. **凭据安全**: 请妥善保管你的API凭据，不要在代码中硬编码
2. **音色限制**: 方言音色可能有使用限制，请参考火山引擎官方文档
3. **网络连接**: 确保服务器能够访问火山引擎API服务
4. **音频格式**: 生成的音频文件为WAV格式，采样率为16000Hz

## 故障排除

### 常见问题

1. **认证失败**: 检查appId、access_token和secret_key是否正确
2. **音色不支持**: 确认使用的音色代码是否正确
3. **网络超时**: 检查网络连接和防火墙设置
4. **音频文件为空**: 检查文本内容和API响应

### 日志查看

系统会记录详细的日志信息，可以通过以下方式查看：

```bash
# 查看应用日志
tail -f logs/xiaozhi.log

# 查看TTS相关日志
grep "VolcengineTtsService" logs/xiaozhi.log
```

## 更新历史

- **2025-07-29**: 初始版本，支持东北话和四川话音色
- 添加了音色映射机制
- 完善了错误处理和日志记录
- 提供了完整的测试用例

## 参考链接

- [火山引擎豆包语音官方文档](https://www.volcengine.com/docs/6561/97465)
- [音色列表](https://www.volcengine.com/docs/6561/97465)
- [鉴权方法](https://www.volcengine.com/docs/6561/1105162)
